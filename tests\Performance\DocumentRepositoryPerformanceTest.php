<?php

namespace App\Tests\Performance;

use App\Repository\DocumentRepository;
use App\Service\PerformanceMonitoringService;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

/**
 * Tests de performance pour DocumentRepository
 * Ces tests vérifient que les optimisations réduisent effectivement le nombre de requêtes et le temps d'exécution
 */
class DocumentRepositoryPerformanceTest extends KernelTestCase
{
    private DocumentRepository $documentRepository;
    private PerformanceMonitoringService $performanceService;

    protected function setUp(): void
    {
        self::bootKernel();
        $container = static::getContainer();
        
        $this->documentRepository = $container->get(DocumentRepository::class);
        $this->performanceService = $container->get(PerformanceMonitoringService::class);
    }

    /**
     * Test de performance pour findActiveDocumentsInStep
     */
    public function testFindActiveDocumentsInStepPerformance(): void
    {
        $this->performanceService->startMonitoring('findActiveDocumentsInStep');
        
        $documents = $this->documentRepository->findActiveDocumentsInStep('Costing');
        
        $report = $this->performanceService->stopMonitoring('findActiveDocumentsInStep');
        
        // Vérifications de performance
        $this->assertLessThan(5000, $report['execution_time_ms'], 'L\'exécution ne doit pas dépasser 5 secondes');
        $this->assertLessThan(50, $report['query_count'], 'Le nombre de requêtes doit être inférieur à 50');
        $this->assertLessThan(256, $report['memory_usage_mb'], 'L\'utilisation mémoire doit être inférieure à 256MB');
        
        // Vérifier qu'il n'y a pas de problèmes N+1
        $hasN1Issues = false;
        foreach ($report['performance_issues'] as $issue) {
            if ($issue['type'] === 'N+1_QUERIES') {
                $hasN1Issues = true;
                break;
            }
        }
        $this->assertFalse($hasN1Issues, 'Aucun problème N+1 ne doit être détecté');
        
        echo "\nPerformance Report for findActiveDocumentsInStep:\n";
        echo "- Execution time: {$report['execution_time_ms']}ms\n";
        echo "- Query count: {$report['query_count']}\n";
        echo "- Memory usage: {$report['memory_usage_mb']}MB\n";
        echo "- Documents found: " . count($documents) . "\n";
    }

    /**
     * Test de performance pour findActiveDocumentsInLogisticsSteps
     */
    public function testFindActiveDocumentsInLogisticsStepsPerformance(): void
    {
        $this->performanceService->startMonitoring('findActiveDocumentsInLogisticsSteps');
        
        $documents = $this->documentRepository->findActiveDocumentsInLogisticsSteps();
        
        $report = $this->performanceService->stopMonitoring('findActiveDocumentsInLogisticsSteps');
        
        // Vérifications de performance
        $this->assertLessThan(5000, $report['execution_time_ms'], 'L\'exécution ne doit pas dépasser 5 secondes');
        $this->assertLessThan(50, $report['query_count'], 'Le nombre de requêtes doit être inférieur à 50');
        
        echo "\nPerformance Report for findActiveDocumentsInLogisticsSteps:\n";
        echo "- Execution time: {$report['execution_time_ms']}ms\n";
        echo "- Query count: {$report['query_count']}\n";
        echo "- Memory usage: {$report['memory_usage_mb']}MB\n";
        echo "- Documents found: " . count($documents) . "\n";
    }

    /**
     * Test de performance pour countDocumentsByWorkflowStepCached
     */
    public function testCountDocumentsByWorkflowStepCachedPerformance(): void
    {
        $this->performanceService->startMonitoring('countDocumentsByWorkflowStepCached');
        
        $counts = $this->documentRepository->countDocumentsByWorkflowStepCached();
        
        $report = $this->performanceService->stopMonitoring('countDocumentsByWorkflowStepCached');
        
        // Vérifications de performance
        $this->assertLessThan(3000, $report['execution_time_ms'], 'L\'exécution ne doit pas dépasser 3 secondes');
        $this->assertLessThan(30, $report['query_count'], 'Le nombre de requêtes doit être inférieur à 30');
        
        echo "\nPerformance Report for countDocumentsByWorkflowStepCached:\n";
        echo "- Execution time: {$report['execution_time_ms']}ms\n";
        echo "- Query count: {$report['query_count']}\n";
        echo "- Memory usage: {$report['memory_usage_mb']}MB\n";
        echo "- Workflow steps counted: " . count($counts) . "\n";
    }

    /**
     * Test de performance pour findDocumentsToValidateByUser
     */
    public function testFindDocumentsToValidateByUserPerformance(): void
    {
        // Créer un utilisateur de test ou utiliser un existant
        $entityManager = static::getContainer()->get('doctrine')->getManager();
        $userRepository = $entityManager->getRepository('App\Entity\User');
        $user = $userRepository->findOneBy([]);
        
        if (!$user) {
            $this->markTestSkipped('Aucun utilisateur trouvé pour le test');
        }
        
        $this->performanceService->startMonitoring('findDocumentsToValidateByUser');
        
        $documents = $this->documentRepository->findDocumentsToValidateByUser($user);
        
        $report = $this->performanceService->stopMonitoring('findDocumentsToValidateByUser');
        
        // Vérifications de performance
        $this->assertLessThan(3000, $report['execution_time_ms'], 'L\'exécution ne doit pas dépasser 3 secondes');
        $this->assertLessThan(20, $report['query_count'], 'Le nombre de requêtes doit être inférieur à 20');
        
        echo "\nPerformance Report for findDocumentsToValidateByUser:\n";
        echo "- Execution time: {$report['execution_time_ms']}ms\n";
        echo "- Query count: {$report['query_count']}\n";
        echo "- Memory usage: {$report['memory_usage_mb']}MB\n";
        echo "- Documents found: " . count($documents) . "\n";
    }

    /**
     * Test de performance pour findByCurrentStepNative
     */
    public function testFindByCurrentStepNativePerformance(): void
    {
        $this->performanceService->startMonitoring('findByCurrentStepNative');
        
        $documents = $this->documentRepository->findByCurrentStepNative('Costing');
        
        $report = $this->performanceService->stopMonitoring('findByCurrentStepNative');
        
        // Vérifications de performance
        $this->assertLessThan(4000, $report['execution_time_ms'], 'L\'exécution ne doit pas dépasser 4 secondes');
        $this->assertLessThan(30, $report['query_count'], 'Le nombre de requêtes doit être inférieur à 30');
        
        echo "\nPerformance Report for findByCurrentStepNative:\n";
        echo "- Execution time: {$report['execution_time_ms']}ms\n";
        echo "- Query count: {$report['query_count']}\n";
        echo "- Memory usage: {$report['memory_usage_mb']}MB\n";
        echo "- Documents found: " . count($documents) . "\n";
    }

    /**
     * Test de comparaison entre l'ancienne et la nouvelle méthode
     */
    public function testPerformanceComparison(): void
    {
        // Test de l'ancienne méthode (simulée)
        $this->performanceService->startMonitoring('old_method_simulation');
        
        // Simuler l'ancienne méthode avec findAll()
        $allDocuments = $this->documentRepository->createQueryBuilder('d')
            ->setMaxResults(100) // Limiter pour le test
            ->getQuery()
            ->getResult();
        
        $filteredDocuments = [];
        foreach ($allDocuments as $document) {
            $currentSteps = $document->getCurrentSteps();
            if (isset($currentSteps['Costing'])) {
                $filteredDocuments[] = $document;
            }
        }
        
        $oldReport = $this->performanceService->stopMonitoring('old_method_simulation');
        
        // Test de la nouvelle méthode optimisée
        $this->performanceService->startMonitoring('new_method_optimized');
        
        $optimizedDocuments = $this->documentRepository->findActiveDocumentsInStep('Costing');
        
        $newReport = $this->performanceService->stopMonitoring('new_method_optimized');
        
        // Comparaison des performances
        echo "\nPerformance Comparison:\n";
        echo "Old method:\n";
        echo "- Execution time: {$oldReport['execution_time_ms']}ms\n";
        echo "- Query count: {$oldReport['query_count']}\n";
        echo "- Memory usage: {$oldReport['memory_usage_mb']}MB\n";
        echo "- Documents found: " . count($filteredDocuments) . "\n";
        
        echo "\nNew method:\n";
        echo "- Execution time: {$newReport['execution_time_ms']}ms\n";
        echo "- Query count: {$newReport['query_count']}\n";
        echo "- Memory usage: {$newReport['memory_usage_mb']}MB\n";
        echo "- Documents found: " . count($optimizedDocuments) . "\n";
        
        // Vérifier que la nouvelle méthode est plus performante
        $this->assertLessThan($oldReport['execution_time_ms'], $newReport['execution_time_ms'], 
            'La nouvelle méthode doit être plus rapide');
        $this->assertLessThan($oldReport['query_count'], $newReport['query_count'], 
            'La nouvelle méthode doit utiliser moins de requêtes');
    }

    /**
     * Test de stress pour vérifier la stabilité sous charge
     */
    public function testStressTest(): void
    {
        $this->performanceService->startMonitoring('stress_test');
        
        $steps = ['Costing', 'Quality', 'Assembly', 'Machining', 'Planning'];
        $totalDocuments = 0;
        
        // Exécuter plusieurs requêtes en parallèle
        foreach ($steps as $step) {
            $documents = $this->documentRepository->findActiveDocumentsInStep($step);
            $totalDocuments += count($documents);
        }
        
        $report = $this->performanceService->stopMonitoring('stress_test');
        
        // Vérifications de stabilité
        $this->assertLessThan(10000, $report['execution_time_ms'], 'Le test de stress ne doit pas dépasser 10 secondes');
        $this->assertLessThan(512, $report['memory_usage_mb'], 'L\'utilisation mémoire doit rester raisonnable');
        
        echo "\nStress Test Report:\n";
        echo "- Total execution time: {$report['execution_time_ms']}ms\n";
        echo "- Total query count: {$report['query_count']}\n";
        echo "- Peak memory usage: {$report['peak_memory_mb']}MB\n";
        echo "- Total documents processed: {$totalDocuments}\n";
        echo "- Steps tested: " . implode(', ', $steps) . "\n";
    }
}
