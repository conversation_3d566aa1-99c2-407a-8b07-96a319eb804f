<?php

namespace App\Repository;

use App\Entity\Document;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\ORM\EntityManagerInterface;

/**
 * @extends ServiceEntityRepository<Document>
 */
class DocumentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Document::class);
    }

//    /**
//     * @return Document[] Returns an array of Document objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('d.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?Document
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }

// findByCurrentStep(string $step)

// public function findByCurrentStepNative(string $key): array
// {
//     // dd($key);
//     $conn = $this->getEntityManager()->getConnection();

//     // Construct the JSON path dynamically
//     $path = '$."' . $key . '"';

//     $sql = "SELECT *
//             FROM document d
//             WHERE JSON_EXTRACT(d.current_steps, :jsonPath) IS NOT NULL";

//     $stmt = $conn->prepare($sql);
//     $stmt->bindValue('jsonPath', $path);
//     $resultSet = $stmt->executeQuery();

//     return $resultSet->fetchAllAssociative();
// }

public function findByCurrentStepNative(string $position)
{
    // #[ORM\Column(type: 'json')]
    // private array $currentSteps = [];
    $documents = $this->findAll();
    $filteredDocuments = [];

    foreach ($documents as $document) {
        $currentSteps = $document->getCurrentSteps();
        foreach ($currentSteps as $step=>$key) {
            if (strpos($step, $position) !== false) {
                $filteredDocuments[] = $document;
                break;
            }
        }
    }

    return $filteredDocuments;
}

    public function findLatestByReference(string $reference): ?Document
    {
        // Utiliser une requête DQL plus explicite pour récupérer toutes les propriétés
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
            ->andWhere('d.reference = :reference')
            ->setParameter('reference', $reference)
            ->orderBy('d.refRev', 'DESC')
            ->setMaxResults(1);

        $query = $qb->getQuery();
        $result = $query->getOneOrNullResult();

        if ($result) {
            // Log pour déboguer
            error_log('Document trouvé dans le repository : ' . $result->getReference() . ' - ' . $result->getRefRev());
            error_log('Material : ' . ($result->getMaterial() ?? 'null'));
            error_log('Ex : ' . ($result->getEx() ?? 'null'));
            error_log('MaterialType : ' . ($result->getMaterialType() ?? 'null'));
            error_log('Action : ' . ($result->getAction() ?? 'null'));
            error_log('HTS : ' . ($result->getHts() ?? 'null'));
            error_log('ECCN : ' . ($result->getEccn() ?? 'null'));
            error_log('RDO : ' . ($result->getRdo() ?? 'null'));
            error_log('CustDrawing : ' . ($result->getCustDrawing() ?? 'null'));
        }

        return $result;
    }

    /**
     * Trouve des documents similaires en fonction du type de document, du type de processus et du type de matériau
     */
    public function findSimilarDocuments(?string $docType, ?string $procType, ?string $materialType, int $limit = 20): array
    {
        $qb = $this->createQueryBuilder('d');

        if ($docType) {
            $qb->andWhere('d.docType = :docType')
               ->setParameter('docType', $docType);
        }

        if ($procType) {
            $qb->andWhere('d.procType = :procType')
               ->setParameter('procType', $procType);
        }

        if ($materialType) {
            $qb->andWhere('d.Material_Type = :materialType')
               ->setParameter('materialType', $materialType);
        }

        return $qb->setMaxResults($limit)
                 ->getQuery()
                 ->getResult();
    }

    /**
     * Trouve des documents par période
     */
    public function findByPeriod(\DateTime $startDate, \DateTime $endDate): array
    {
        $documents = $this->findAll();
        $result = [];

        foreach ($documents as $document) {
            $timestamps = $document->getRawStateTimestamps();
            if (!$timestamps) {
                continue;
            }

            $firstDate = null;

            foreach ($timestamps as $state => $entries) {
                if (is_array($entries)) {
                    foreach ($entries as $entry) {
                        $enterDate = new \DateTime($entry['enter']);
                        if ($firstDate === null || $enterDate < $firstDate) {
                            $firstDate = $enterDate;
                        }
                    }
                } elseif (is_string($entries)) {
                    // Ancien format
                    $date = new \DateTime($entries);
                    if ($firstDate === null || $date < $firstDate) {
                        $firstDate = $date;
                    }
                }
            }

            if ($firstDate && $firstDate >= $startDate && $firstDate <= $endDate) {
                $result[] = $document;
            }
        }

        return $result;
    }

    /**
     * Trouve les documents à valider par un utilisateur
     */
    public function findDocumentsToValidateByUser($user): array
    {
        $documents = $this->findAll();
        $result = [];

        foreach ($documents as $document) {
            $visas = $document->getVisas();

            if (!$visas) {
                continue;
            }

            foreach ($visas as $visa) {
                if ($visa->getValidator() && $visa->getValidator()->getId() === $user->getId() && !$visa->getDateVisa()) {
                    $result[] = $document;
                    break;
                }
            }
        }

        return $result;
    }

    /**
     * Trouve les documents à réviser par un utilisateur
     */
    public function findDocumentsToReviewByUser($user): array
    {
        $documents = $this->findAll();
        $result = [];

        foreach ($documents as $document) {
            // Vérifier si l'utilisateur est le superviseur du document
            $isOwner = $document->getSuperviseur() && $document->getSuperviseur()->getId() === $user->getId();

            if (!$isOwner) {
                continue;
            }

            // Vérifier si le document a été rejeté récemment
            $timestamps = $document->getRawStateTimestamps();
            if (!$timestamps) {
                continue;
            }

            $wasRejected = false;

            foreach ($timestamps as $state => $entries) {
                if (is_array($entries)) {
                    foreach ($entries as $entry) {
                        if (isset($entry['from_state']) && strpos(strtolower($entry['from_state']), 'reject') !== false) {
                            $wasRejected = true;
                            break;
                        }
                    }
                }

                if ($wasRejected) {
                    break;
                }
            }

            if ($wasRejected) {
                $result[] = $document;
            }
        }

        return $result;
    }

    /**
     * Trouve les documents traités entre deux dates
     */
    public function findDocumentsProcessedBetween(\DateTime $startDate, \DateTime $endDate): array
    {
        $documents = $this->findAll();
        $result = [];

        foreach ($documents as $document) {
            $timestamps = $document->getRawStateTimestamps();
            if (!$timestamps) {
                continue;
            }

            $lastDate = null;

            foreach ($timestamps as $state => $entries) {
                if (is_array($entries)) {
                    foreach ($entries as $entry) {
                        if (isset($entry['exit']) && $entry['exit'] !== null) {
                            $exitDate = new \DateTime($entry['exit']);
                            if ($lastDate === null || $exitDate > $lastDate) {
                                $lastDate = $exitDate;
                            }
                        }
                    }
                }
            }

            if ($lastDate && $lastDate >= $startDate && $lastDate <= $endDate) {
                $result[] = $document;
            }
        }

        return $result;
    }

    /**
     * Compte les documents par étape de workflow de manière optimisée
     * Utilise des requêtes SQL natives pour contourner les limitations de Doctrine avec JSON
     */
    public function countDocumentsByWorkflowStep(): array
    {
        try {
            return $this->countDocumentsByWorkflowStepNative();
        } catch (\Exception $e) {
            // Fallback vers l'ancienne méthode en cas d'erreur
            return $this->countDocumentsByWorkflowStepFallback();
        }
    }

    /**
     * Version native avec requêtes SQL optimisées
     */
    private function countDocumentsByWorkflowStepNative(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        // Liste des étapes de workflow connues
        $workflowSteps = [
            'BE_0', 'BE_1', 'BE', 'Produit', 'Qual_Logistique', 'Logistique',
            'Metro', 'Quality', 'Achat_Rfq', 'Achat_RoHs_REACH', 'Assembly',
            'Machining', 'Molding', 'Methode_assemblage', 'Planning', 'Core_Data',
            'Project', 'Achat_F30', 'Prod_Data', 'Achat_FIA', 'Achat_Hts',
            'Saisie_hts', 'Costing', 'GID', 'Indus', 'methode_Labo', 'QProd',
            'Tirage_Plans'
        ];

        $counts = [];

        foreach ($workflowSteps as $step) {
            if ($step === 'Qual_Logistique' || $step === 'Logistique') {
                // Cas spécial pour les étapes logistiques (traité une seule fois)
                if (!isset($counts['Qual_Logistique'])) {
                    $sql = "
                        SELECT COUNT(DISTINCT d.id) as count_docs
                        FROM document d
                        WHERE (
                            JSON_EXTRACT(d.current_steps, '$.\"Qual_Logistique\"') IS NOT NULL
                            OR JSON_EXTRACT(d.current_steps, '$.\"Logistique\"') IS NOT NULL
                        )
                        AND NOT (
                            EXISTS (SELECT 1 FROM visa v1 WHERE v1.released_drawing_id = d.id AND v1.name = 'visa_Qual_Logistique' AND v1.status = 'valid')
                            AND EXISTS (SELECT 1 FROM visa v2 WHERE v2.released_drawing_id = d.id AND v2.name = 'visa_Logistique' AND v2.status = 'valid')
                        )
                    ";

                    $result = $conn->executeQuery($sql);
                    $count = $result->fetchOne();

                    if ($count > 0) {
                        $counts['Qual_Logistique'] = (int)$count;
                        $counts['Logistique'] = (int)$count;
                    }
                }
            } else {
                // Cas général pour les autres étapes
                $sql = "
                    SELECT COUNT(d.id) as count_docs
                    FROM document d
                    WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
                    AND NOT EXISTS (
                        SELECT 1 FROM visa v
                        WHERE v.released_drawing_id = d.id
                        AND v.name = ?
                        AND v.status = 'valid'
                    )
                ";

                $result = $conn->executeQuery($sql, [
                    '$."' . $step . '"',
                    'visa_' . $step
                ]);
                $count = $result->fetchOne();

                if ($count > 0) {
                    $counts[$step] = (int)$count;
                }
            }
        }

        return $counts;
    }

    /**
     * Version de fallback utilisant l'ancienne logique mais optimisée
     */
    private function countDocumentsByWorkflowStepFallback(): array
    {
        // Charger tous les documents une seule fois
        $documents = $this->findAll();
        $count = [];

        foreach ($documents as $document) {
            foreach ($document->getCurrentSteps() as $step => $value) {
                if ($step === 'Qual_Logistique' || $step === 'Logistique') {
                    if ($document->hasVisa('visa_Qual_Logistique') && $document->hasVisa('visa_Logistique')) {
                        continue;
                    }
                } else {
                    if ($document->hasVisa('visa_'.$step)) {
                        continue;
                    }
                }

                if (!isset($count[$step])) {
                    $count[$step] = 0;
                }
                $count[$step]++;
            }
        }

        return $count;
    }

    /**
     * Version avec cache du comptage des documents par étape
     * Cache pendant 30 secondes pour éviter les requêtes répétées
     */
    public function countDocumentsByWorkflowStepCached(): array
    {
        static $cache = null;
        static $cacheTime = null;

        $now = time();

        // Si le cache est vide ou expiré (2 minutes)
        if ($cache === null || $cacheTime === null || ($now - $cacheTime) > 120) {
            $cache = $this->countDocumentsByWorkflowStep();
            $cacheTime = $now;
        }

        return $cache;
    }

    /**
     * Trouve les documents actifs dans une étape spécifique (sans visa correspondant)
     * Utilise une requête SQL native pour contourner les limitations de Doctrine avec JSON
     */
    public function findActiveDocumentsInStep(string $step): array
    {
        try {
            return $this->findActiveDocumentsInStepNative($step);
        } catch (\Exception $e) {
            // Fallback vers l'ancienne méthode
            return $this->findActiveDocumentsInStepFallback($step);
        }
    }

    private function findActiveDocumentsInStepNative(string $step): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.*
            FROM document d
            WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 FROM visa v
                WHERE v.released_drawing_id = d.id
                AND v.name = ?
                AND v.status = 'valid'
            )
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql, [
            '$."' . $step . '"',
            'visa_' . $step
        ]);

        $documentsData = $result->fetchAllAssociative();

        // Convertir les résultats en entités Document
        $documents = [];
        foreach ($documentsData as $data) {
            $document = $this->find($data['id']);
            if ($document) {
                $documents[] = $document;
            }
        }

        return $documents;
    }

    private function findActiveDocumentsInStepFallback(string $step): array
    {
        $documents = $this->findByCurrentStepNative($step);
        return array_filter($documents, function ($document) use ($step) {
            return !$document->hasVisa('visa_'.$step);
        });
    }

    /**
     * Trouve les documents actifs dans les étapes logistiques
     * (Qual_Logistique ou Logistique sans avoir les deux visas)
     */
    public function findActiveDocumentsInLogisticsSteps(): array
    {
        try {
            return $this->findActiveDocumentsInLogisticsStepsNative();
        } catch (\Exception $e) {
            // Fallback vers l'ancienne méthode
            return $this->findActiveDocumentsInLogisticsStepsFallback();
        }
    }

    private function findActiveDocumentsInLogisticsStepsNative(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.*
            FROM document d
            WHERE (
                JSON_EXTRACT(d.current_steps, '$.\"Qual_Logistique\"') IS NOT NULL
                OR JSON_EXTRACT(d.current_steps, '$.\"Logistique\"') IS NOT NULL
            )
            AND NOT (
                EXISTS (SELECT 1 FROM visa v1 WHERE v1.released_drawing_id = d.id AND v1.name = 'visa_Qual_Logistique' AND v1.status = 'valid')
                AND EXISTS (SELECT 1 FROM visa v2 WHERE v2.released_drawing_id = d.id AND v2.name = 'visa_Logistique' AND v2.status = 'valid')
            )
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql);
        $documentsData = $result->fetchAllAssociative();

        // Convertir les résultats en entités Document
        $documents = [];
        foreach ($documentsData as $data) {
            $document = $this->find($data['id']);
            if ($document) {
                $documents[] = $document;
            }
        }

        return $documents;
    }

    private function findActiveDocumentsInLogisticsStepsFallback(): array
    {
        $document0 = $this->findByCurrentStepNative('Qual_Logistique');
        $document1 = $this->findByCurrentStepNative('Logistique');
        $documents = array_unique(array_merge($document0, $document1), SORT_REGULAR);

        return array_filter($documents, function ($document) {
            return !$document->hasVisa('visa_Qual_Logistique') || !$document->hasVisa('visa_Logistique');
        });
    }

    /**
     * Version optimisée de findByCurrentStepNative utilisant une requête SQL native
     */
    public function findByCurrentStepOptimized(string $step): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.*
            FROM document d
            WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql, ['$."' . $step . '"']);
        $documentsData = $result->fetchAllAssociative();

        // Convertir les résultats en entités Document
        $documents = [];
        foreach ($documentsData as $data) {
            $document = $this->find($data['id']);
            if ($document) {
                $documents[] = $document;
            }
        }

        return $documents;
    }

    /**
     * Statistiques optimisées pour la navbar avec cache
     */
    public function getNavbarStatsOptimized(): array
    {
        static $cache = null;
        static $cacheTime = null;

        $now = time();

        // Cache pendant 5 minutes pour les stats de navbar
        if ($cache === null || $cacheTime === null || ($now - $cacheTime) > 300) {
            $cache = $this->calculateNavbarStats();
            $cacheTime = $now;
        }

        return $cache;
    }

    /**
     * Calcule les statistiques de navbar de manière optimisée
     */
    private function calculateNavbarStats(): array
    {
        try {
            return $this->calculateNavbarStatsNative();
        } catch (\Exception $e) {
            // Fallback vers l'ancienne méthode
            return $this->calculateNavbarStatsFallback();
        }
    }

    /**
     * Version optimisée avec requêtes SQL natives
     */
    private function calculateNavbarStatsNative(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        // Compter le total de documents
        $totalDocuments = $conn->executeQuery("SELECT COUNT(*) FROM document")->fetchOne();

        // Compter les documents sortis de BE (qui ont des state_timestamps)
        $documentsOutOfBE = $conn->executeQuery("
            SELECT COUNT(*) FROM document
            WHERE state_timestamps IS NOT NULL
            AND state_timestamps != '{}'
            AND state_timestamps != ''
        ")->fetchOne();

        // Calculer les statistiques de temps depuis BE
        $timeStats = $conn->executeQuery("
            SELECT
                AVG(DATEDIFF(NOW(), JSON_UNQUOTE(JSON_EXTRACT(state_timestamps, '$.BE[0].enter')))) as avg_days,
                MAX(DATEDIFF(NOW(), JSON_UNQUOTE(JSON_EXTRACT(state_timestamps, '$.BE[0].enter')))) as max_days
            FROM document
            WHERE JSON_EXTRACT(state_timestamps, '$.BE[0].enter') IS NOT NULL
        ")->fetchAssociative();

        $avgDaysSinceBE = $timeStats['avg_days'] ? round($timeStats['avg_days'], 1) : 0;
        $maxDaysSinceBE = $timeStats['max_days'] ? (int)$timeStats['max_days'] : 0;

        // Trouver le document avec le maximum de jours
        $documentWithMaxDays = null;
        if ($maxDaysSinceBE > 0) {
            $maxDocId = $conn->executeQuery("
                SELECT id FROM document
                WHERE JSON_EXTRACT(state_timestamps, '$.BE[0].enter') IS NOT NULL
                AND DATEDIFF(NOW(), JSON_UNQUOTE(JSON_EXTRACT(state_timestamps, '$.BE[0].enter'))) = ?
                LIMIT 1
            ", [$maxDaysSinceBE])->fetchOne();

            if ($maxDocId) {
                $documentWithMaxDays = $this->find($maxDocId);
            }
        }

        return [
            'totalDocuments' => (int)$totalDocuments,
            'documentsOutOfBE' => (int)$documentsOutOfBE,
            'avgDaysSinceBE' => $avgDaysSinceBE,
            'maxDaysSinceBE' => $maxDaysSinceBE,
            'documentWithMaxDays' => $documentWithMaxDays,
        ];
    }

    /**
     * Version de fallback utilisant l'ancienne logique
     */
    private function calculateNavbarStatsFallback(): array
    {
        // Utiliser une requête plus simple pour éviter de charger tous les documents
        $totalDocuments = $this->createQueryBuilder('d')
            ->select('COUNT(d.id)')
            ->getQuery()
            ->getSingleScalarResult();

        // Charger seulement les documents avec des timestamps pour les calculs
        $documentsWithTimestamps = $this->createQueryBuilder('d')
            ->where('d.stateTimestamps IS NOT NULL')
            ->andWhere('d.stateTimestamps != :empty1')
            ->andWhere('d.stateTimestamps != :empty2')
            ->setParameter('empty1', '{}')
            ->setParameter('empty2', '')
            ->getQuery()
            ->getResult();

        $documentsOutOfBE = 0;
        $totalDaysSinceBE = 0;
        $maxDaysSinceBE = 0;
        $documentWithMaxDays = null;

        foreach ($documentsWithTimestamps as $document) {
            $daysSinceBE = $document->getDaysSinceBE();
            if ($daysSinceBE !== null) {
                $documentsOutOfBE++;
                $totalDaysSinceBE += $daysSinceBE;

                if ($daysSinceBE > $maxDaysSinceBE) {
                    $maxDaysSinceBE = $daysSinceBE;
                    $documentWithMaxDays = $document;
                }
            }
        }

        $avgDaysSinceBE = $documentsOutOfBE > 0 ? round($totalDaysSinceBE / $documentsOutOfBE, 1) : 0;

        return [
            'totalDocuments' => (int)$totalDocuments,
            'documentsOutOfBE' => $documentsOutOfBE,
            'avgDaysSinceBE' => $avgDaysSinceBE,
            'maxDaysSinceBE' => $maxDaysSinceBE,
            'documentWithMaxDays' => $documentWithMaxDays,
        ];
    }
}
