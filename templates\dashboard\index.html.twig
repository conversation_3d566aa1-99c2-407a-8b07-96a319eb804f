{% extends 'base.html.twig' %}

{% block title %}Tableau de bord{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
    .dashboard-header {
        background-color: #f0f4f8;
        border-radius: 4px;
        padding: 15px 20px;
        margin-bottom: 20px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #0275d8;
    }

    .dashboard-widget {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        transition: all 0.2s ease;
        border: 1px solid #e9ecef;
    }

    .dashboard-widget:hover {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-color: #dee2e6;
    }

    .widget-header {
        background-color: #f8f9fa;
        color: #495057;
        padding: 12px 15px;
        border-radius: 4px 4px 0 0;
        font-weight: 600;
        border-bottom: 1px solid #e9ecef;
    }

    .widget-body {
        padding: 15px;
    }

    .stats-number {
        font-size: 2rem;
        font-weight: 600;
        color: #0275d8;
    }

    .stats-label {
        font-size: 0.85rem;
        color: #6c757d;
        font-weight: 500;
    }

    .chart-container {
        position: relative;
        height: 250px;
        margin-top: 10px;
    }

    .notification-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        font-size: 0.7rem;
    }

    .document-list {
        max-height: 300px;
        overflow-y: auto;
        margin-top: 10px;
    }

    .document-item {
        padding: 10px;
        border-bottom: 1px solid #e9ecef;
        transition: background-color 0.2s;
    }

    .document-item:hover {
        background-color: #f8f9fa;
    }

    .document-item:last-child {
        border-bottom: none;
    }

    .risky-document {
        border-left: 4px solid #dc3545;
    }

    .warning-document {
        border-left: 4px solid #ffc107;
    }

    .info-document {
        border-left: 4px solid #0dcaf0;
    }

    .btn-outline-primary, .btn-outline-secondary {
        border-radius: 4px;
        font-weight: 500;
    }

    .dropdown-menu {
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
    }

    .badge {
        font-weight: 500;
        padding: 0.35em 0.65em;
    }

    .table th {
        background-color: #f8f9fa;
        font-weight: 600;
        border-bottom: 2px solid #dee2e6;
    }

    .table td {
        vertical-align: middle;
    }
</style>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block body %}
<div class="container-fluid py-4 px-4 bg-light">
    <!-- En-tête du tableau de bord avec informations utilisateur et statistiques rapides -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="dashboard-welcome p-4 rounded bg-white shadow-sm border-start border-primary border-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 fw-bold text-dark mb-1">
                            <i class="fas fa-tachometer-alt text-primary me-2"></i>
                            Tableau de bord
                        </h1>
                        <p class="text-muted mb-0">Bienvenue, <span class="fw-bold">{{ app.user.prenom }} {{ app.user.nom }}</span></p>
                    </div>
                    <div>
                        <a href="{{ path('app_dashboard_customize') }}" class="btn btn-sm btn-outline-primary me-2">
                            <i class="fas fa-cog"></i> Personnaliser
                        </a>

                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="row h-100">
                <div class="col-6">
                    <div class="dashboard-stat h-100 p-3 rounded bg-white shadow-sm d-flex flex-column justify-content-center align-items-center border-top border-primary border-4">
                        <div class="stat-icon mb-2 rounded-circle bg-primary bg-opacity-10 p-2">
                            <i class="fas fa-file-alt text-primary fa-2x"></i>
                        </div>
                        <div class="stat-value fw-bold h4 mb-0">
                            {% if dashboard_data.document_stats is defined %}
                                {{ dashboard_data.document_stats.total|default('0') }}
                            {% else %}
                                0
                            {% endif %}
                        </div>
                        <div class="stat-label text-muted small">Documents</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="dashboard-stat h-100 p-3 rounded bg-white shadow-sm d-flex flex-column justify-content-center align-items-center border-top border-warning border-4">
                        <div class="stat-icon mb-2 rounded-circle bg-warning bg-opacity-10 p-2">
                            <i class="fas fa-exclamation-triangle text-warning fa-2x"></i>
                        </div>
                        <div class="stat-value fw-bold h4 mb-0">
                            {% if dashboard_data.risky_documents is defined %}
                                {{ dashboard_data.risky_documents|length|default('0') }}
                            {% else %}
                                0
                            {% endif %}
                        </div>
                        <div class="stat-label text-muted small">À risque</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Widgets principaux -->
    <div class="row mb-4">
        {% if widgets.document_stats is defined and widgets.document_stats %}
            <div class="col-md-4 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white py-3 d-flex align-items-center">
                        <div class="icon-circle bg-primary bg-opacity-10 p-2 me-2">
                            <i class="fas fa-chart-pie text-primary"></i>
                        </div>
                        <h5 class="card-title mb-0 fw-bold">Statistiques des documents</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center mb-3">
                            <div class="col-6">
                                <div class="p-3 rounded bg-light">
                                    <div class="h3 fw-bold text-primary mb-0">{{ dashboard_data.document_stats.total }}</div>
                                    <div class="small text-muted">Documents totaux</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 rounded bg-light">
                                    <div class="h3 fw-bold text-primary mb-0">
                                        {% if dashboard_data.document_stats.by_type|length > 0 %}
                                            {% set max_type = null %}
                                            {% set max_count = 0 %}
                                            {% for type, count in dashboard_data.document_stats.by_type %}
                                                {% if count > max_count %}
                                                    {% set max_type = type %}
                                                    {% set max_count = count %}
                                                {% endif %}
                                            {% endfor %}
                                            {{ max_type }}
                                        {% else %}
                                            N/A
                                        {% endif %}
                                    </div>
                                    <div class="small text-muted">Type le plus courant</div>
                                </div>
                            </div>
                        </div>
                        <div class="chart-container mt-4">
                            <canvas id="documentTypeChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        {% if widgets.workflow_stats is defined and widgets.workflow_stats %}
            <div class="col-md-4 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white py-3 d-flex align-items-center">
                        <div class="icon-circle bg-success bg-opacity-10 p-2 me-2">
                            <i class="fas fa-project-diagram text-success"></i>
                        </div>
                        <h5 class="card-title mb-0 fw-bold">Statistiques du workflow</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container mb-3">
                            <canvas id="stateDistributionChart"></canvas>
                        </div>
                        <h6 class="mt-4 mb-3 fw-bold text-dark">États les plus lents (jours)</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th class="border-0">État</th>
                                        <th class="border-0 text-end">Temps moyen</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for state, time in dashboard_data.workflow_stats.average_time_by_state|slice(0, 5) %}
                                        <tr>
                                            <td>{{ state }}</td>
                                            <td class="text-end fw-bold">{{ time }} jours</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        {% if widgets.risky_documents is defined and widgets.risky_documents %}
            <div class="col-md-4 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white py-3 d-flex align-items-center">
                        <div class="icon-circle bg-danger bg-opacity-10 p-2 me-2">
                            <i class="fas fa-exclamation-triangle text-danger"></i>
                        </div>
                        <h5 class="card-title mb-0 fw-bold">Documents à risque</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="document-list">
                            {% if dashboard_data.risky_documents|length > 0 %}
                                {% for risky in dashboard_data.risky_documents|slice(0, 5) %}
                                    <div class="document-item p-3 {% if risky.days_in_state > 14 %}border-start border-danger border-4{% elseif risky.days_in_state > 7 %}border-start border-warning border-4{% else %}border-start border-info border-4{% endif %}">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <div class="fw-bold text-dark">{{ risky.document.reference }}</div>
                                                <div class="small text-muted">{{ risky.document.refTitleFra|default('Sans titre')|length > 30 ? risky.document.refTitleFra|slice(0, 30) ~ '...' : risky.document.refTitleFra|default('Sans titre') }}</div>
                                            </div>
                                            <div class="text-end">
                                                <div class="badge {% if risky.days_in_state > 14 %}bg-danger{% elseif risky.days_in_state > 7 %}bg-warning{% else %}bg-info{% endif %} rounded-pill px-3">{{ risky.days_in_state }} jours</div>
                                                <div class="small text-muted d-block mt-1">État: {{ risky.state }}</div>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                                {% if dashboard_data.risky_documents|length > 5 %}
                                    <div class="text-center py-3 border-top">
                                        <a href="{{ path('app_forecast') }}" class="text-decoration-none">
                                            <i class="fas fa-arrow-right me-1"></i> Voir tous les documents à risque ({{ dashboard_data.risky_documents|length }})
                                        </a>
                                    </div>
                                {% endif %}
                            {% else %}
                                <div class="text-center py-4">
                                    <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                                    <p class="mb-0">Aucun document à risque</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Widgets secondaires -->
    <div class="row mb-4">
        {% if widgets.recent_documents is defined and widgets.recent_documents %}
            <div class="col-md-6 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white py-3 d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="icon-circle bg-info bg-opacity-10 p-2 me-2">
                                <i class="fas fa-file-alt text-info"></i>
                            </div>
                            <h5 class="card-title mb-0 fw-bold">Documents récents</h5>
                        </div>
                        <a href="#" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-search"></i> Voir tous
                        </a>
                    </div>
                    <div class="card-body p-0">
                        <div class="document-list">
                            {% if dashboard_data.recent_documents|length > 0 %}
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th class="border-0">Référence</th>
                                                <th class="border-0">Titre</th>
                                                <th class="border-0">Type</th>
                                                <th class="border-0">État</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for document in dashboard_data.recent_documents|slice(0, 5) %}
                                                <tr>
                                                    <td class="fw-bold">{{ document.reference }}</td>
                                                    <td>{{ document.refTitleFra|default('Sans titre')|length > 30 ? document.refTitleFra|slice(0, 30) ~ '...' : document.refTitleFra|default('Sans titre') }}</td>
                                                    <td>
                                                        <span class="badge bg-primary bg-opacity-10 text-primary px-2 py-1 rounded-pill">
                                                            {{ document.docType|default('N/A') }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        {% set currentSteps = document.currentSteps %}
                                                        {% if currentSteps|length > 0 %}
                                                            <span class="badge bg-secondary bg-opacity-10 text-secondary px-2 py-1 rounded-pill">
                                                                {{ currentSteps|keys|first }}
                                                            </span>
                                                        {% else %}
                                                            <span class="badge bg-secondary bg-opacity-10 text-secondary px-2 py-1 rounded-pill">
                                                                N/A
                                                            </span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="text-center py-4">
                                    <i class="fas fa-file-alt text-muted fa-3x mb-3"></i>
                                    <p class="mb-0">Aucun document récent</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        {% if widgets.processing_time_trends is defined and widgets.processing_time_trends %}
            <div class="col-md-6 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white py-3 d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="icon-circle bg-warning bg-opacity-10 p-2 me-2">
                                <i class="fas fa-chart-line text-warning"></i>
                            </div>
                            <h5 class="card-title mb-0 fw-bold">Tendances des temps de traitement</h5>
                        </div>
                        <a href="{{ path('app_forecast') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-chart-bar"></i> Détails
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="processingTimeTrendsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>

    {% if widgets.document_distribution is defined and widgets.document_distribution %}
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3 d-flex align-items-center">
                        <div class="icon-circle bg-purple bg-opacity-10 p-2 me-2">
                            <i class="fas fa-users text-purple"></i>
                        </div>
                        <h5 class="card-title mb-0 fw-bold">Répartition des documents</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="fw-bold mb-3 text-dark">Par état actuel</h6>
                                <div class="chart-container">
                                    <canvas id="departmentDistributionChart"></canvas>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="fw-bold text-dark mb-0">Par utilisateur (Top 10)</h6>
                                    <select id="userDistributionType" class="form-select form-select-sm" style="width: auto;">
                                        <option value="by_supervisor">Par nombre de documents</option>
                                        <option value="by_recent_activity">Par activité récente</option>
                                        <option value="by_processing_time">Par temps de traitement</option>
                                    </select>
                                </div>
                                <div class="chart-container">
                                    <canvas id="userDistributionChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Nouveaux widgets -->

    {% if widgets.recent_activities is defined and widgets.recent_activities %}
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white py-3 d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="icon-circle bg-info bg-opacity-10 p-2 me-2">
                                <i class="fas fa-history text-info"></i>
                            </div>
                            <h5 class="card-title mb-0 fw-bold">Activité récente</h5>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="activity-list">
                            {% if dashboard_data.recent_activities is defined and dashboard_data.recent_activities|length > 0 %}
                                <div class="list-group list-group-flush">
                                    {% for activity in dashboard_data.recent_activities %}
                                        <div class="list-group-item border-0 border-bottom py-3">
                                            <div class="d-flex">
                                                <div class="me-3">
                                                    <div class="icon-circle bg-{{ activity.color }} bg-opacity-10 p-2">
                                                        <i class="fas {{ activity.icon }} text-{{ activity.color }}"></i>
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <div class="d-flex justify-content-between align-items-start">
                                                        <h6 class="mb-1 fw-bold">{{ activity.message }}</h6>
                                                        <small class="text-muted" title="{{ activity.date|date('d/m/Y H:i') }}">
                                                            {{ activity.time_ago }} <span class="ms-1">{{ activity.exact_time }}</span>
                                                        </small>
                                                    </div>
                                                    <p class="mb-1">
                                                        <a href="{{ path('app_document_place', {'place': activity.document.getCurrentSteps()|keys|first}) }}" class="text-decoration-none">{{ activity.reference }}</a>
                                                        - {{ activity.title|default('Sans titre')|length > 40 ? activity.title|slice(0, 40) ~ '...' : activity.title|default('Sans titre') }}
                                                    </p>
                                                    {% if activity.user %}
                                                        <small class="text-muted">
                                                            <i class="fas fa-user me-1"></i> {{ activity.user.prenom }} {{ activity.user.nom }}
                                                        </small>
                                                    {% endif %}
                                                    {% if activity.content is defined and activity.content %}
                                                        <div class="mt-2 p-2 bg-light rounded">
                                                            <small>{{ activity.content|length > 100 ? activity.content|slice(0, 100) ~ '...' : activity.content }}</small>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <div class="text-center py-4">
                                    <i class="fas fa-history text-muted fa-3x mb-3"></i>
                                    <p class="mb-0">Aucune activité récente</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            {% if widgets.pending_tasks is defined and widgets.pending_tasks %}
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-white py-3 d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <div class="icon-circle bg-danger bg-opacity-10 p-2 me-2">
                                    <i class="fas fa-tasks text-danger"></i>
                                </div>
                                <h5 class="card-title mb-0 fw-bold">Mes tâches en attente</h5>
                            </div>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('app_user_managed_places') }}" class="btn btn-sm btn-outline-secondary me-2" title="Configurer mes places gérées">
                                    <i class="fas fa-cog"></i>
                                </a>
                                {# <span class="badge bg-danger rounded-pill px-3 py-2">{{ dashboard_data.pending_tasks.total }}</span> #}
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <ul class="nav nav-tabs nav-fill" id="pendingTasksTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="my-activities-tab" data-bs-toggle="tab" data-bs-target="#my-activities" type="button" role="tab" aria-controls="my-activities" aria-selected="true">
                                        <i class="fas fa-history me-1"></i> Mes activités
                                        <span class="badge bg-primary ms-1">{{ dashboard_data.pending_tasks.recent_activities|length }}</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="managed-places-tab" data-bs-toggle="tab" data-bs-target="#managed-places" type="button" role="tab" aria-controls="managed-places" aria-selected="false">
                                        <i class="fas fa-eye me-1"></i> Places supervisées
                                        {% if dashboard_data.pending_tasks.managed_places|length > 0 %}
                                        <span class="badge bg-danger ms-1">
                                            {% set total_managed = 0 %}
                                            {% for place, data in dashboard_data.pending_tasks.managed_places %}
                                                {% set total_managed = total_managed + data.count %}
                                            {% endfor %}
                                            {{ total_managed }}
                                        </span>
                                        {% endif %}
                                    </button>
                                </li>
                            </ul>
                            <div class="tab-content" id="pendingTasksTabContent">
                                <div class="tab-pane fade show active" id="my-activities" role="tabpanel" aria-labelledby="my-activities-tab">
                                    {% if dashboard_data.pending_tasks.recent_activities|length > 0 %}
                                        <div class="list-group list-group-flush">
                                            {% for activity in dashboard_data.pending_tasks.recent_activities %}
                                                <div class="list-group-item border-0 border-bottom py-3">
                                                    <div class="d-flex align-items-start">
                                                        <div class="activity-icon me-3 mt-1">
                                                            <div class="icon-circle bg-{{ activity.color }} bg-opacity-10 p-2">
                                                                <i class="fas {{ activity.icon }} text-{{ activity.color }}"></i>
                                                            </div>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <div class="d-flex justify-content-between align-items-center mb-1">
                                                                <h6 class="mb-0 fw-bold">{{ activity.reference }}</h6>
                                                                <small class="text-muted">{{ activity.time_ago }} ({{ activity.exact_time }})</small>
                                                            </div>
                                                            <p class="mb-1 small">{{ activity.title|default('Sans titre')|length > 40 ? activity.title|slice(0, 40) ~ '...' : activity.title|default('Sans titre') }}</p>

                                                            <p class="mb-1 small text-{{ activity.color }}">
                                                                <i class="fas {{ activity.icon }} me-1"></i> {{ activity.message }}
                                                                {% if activity.content is defined and activity.content %}
                                                                    : "{{ activity.content|length > 50 ? activity.content|slice(0, 50) ~ '...' : activity.content }}"
                                                                {% endif %}
                                                            </p>

                                                            {% if activity.document.getCurrentSteps %}
                                                                <span class="badge bg-secondary bg-opacity-10 text-secondary">
                                                                    {% set current_state = activity.document.getCurrentSteps|keys|first %}
                                                                    {{ current_state }}
                                                                </span>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                </div>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        <div class="text-center py-4">
                                            <i class="fas fa-history text-secondary fa-3x mb-3"></i>
                                            <p class="mb-0">Aucune activité récente</p>
                                        </div>
                                    {% endif %}
                                </div>



                                <div class="tab-pane fade" id="managed-places" role="tabpanel" aria-labelledby="managed-places-tab">
                                    {% if dashboard_data.pending_tasks.managed_places|length == 0 %}
                                        <div class="text-center py-4">
                                            <i class="fas fa-cog text-secondary fa-3x mb-3"></i>
                                            <p class="mb-2">Vous n'avez pas encore configuré de places supervisées</p>
                                            <a href="{{ path('app_user_managed_places') }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-cog me-1"></i> Configurer mes places
                                            </a>
                                        </div>
                                    {% else %}
                                        <div class="accordion" id="managedPlacesAccordion">
                                            {% for place, data in dashboard_data.pending_tasks.managed_places %}
                                                <div class="accordion-item border-0 border-bottom">
                                                    <h2 class="accordion-header" id="heading{{ place }}">
                                                        <button class="accordion-button collapsed py-3" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ place }}" aria-expanded="false" aria-controls="collapse{{ place }}">
                                                            <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                                                <span class="fw-bold">{{ place }}</span>
                                                                <span class="badge bg-danger rounded-pill">{{ data.count }}</span>
                                                            </div>
                                                        </button>
                                                    </h2>
                                                    <div id="collapse{{ place }}" class="accordion-collapse collapse" aria-labelledby="heading{{ place }}" data-bs-parent="#managedPlacesAccordion">
                                                        <div class="accordion-body p-0">
                                                            <div class="list-group list-group-flush">
                                                                {% for task in data.documents %}
                                                                    <div class="list-group-item border-0 border-bottom py-3">
                                                                        <div class="d-flex justify-content-between align-items-center">
                                                                            <div>
                                                                                <h6 class="mb-1 fw-bold">{{ task.document.reference }}</h6>
                                                                                <p class="mb-1 small">{{ task.document.refTitleFra|default('Sans titre')|length > 40 ? task.document.refTitleFra|slice(0, 40) ~ '...' : task.document.refTitleFra|default('Sans titre') }}</p>
                                                                                <span class="badge bg-secondary bg-opacity-10 text-secondary">{{ task.state }}</span>
                                                                            </div>
                                                                            <div class="text-end">
                                                                                <div class="progress-circle" data-value="{{ task.urgency }}" data-size="40" data-thickness="4" data-color="{{ task.urgency > 75 ? '#dc3545' : (task.urgency > 50 ? '#ffc107' : '#28a745') }}">
                                                                                    <div class="progress-circle-value">{{ task.urgency }}</div>
                                                                                </div>
                                                                                <a href="{{ path('app_document_place', {'place': task.state}) }}" class="btn btn-sm btn-outline-primary mt-2">Traiter</a>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                {% endfor %}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    {% endif %}

    {% if widgets.department_performance is defined and widgets.department_performance %}
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3 d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="icon-circle bg-success bg-opacity-10 p-2 me-2">
                                <i class="fas fa-building text-success"></i>
                            </div>
                            <h5 class="card-title mb-0 fw-bold">Performance par département</h5>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-outline-primary active" data-metric="processing_time">Temps de traitement</button>
                            <button type="button" class="btn btn-sm btn-outline-primary" data-metric="document_count">Nombre de documents</button>
                            <button type="button" class="btn btn-sm btn-outline-primary" data-metric="rejection_rate">Taux de rejet</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" style="height: 350px;">
                            <canvas id="departmentPerformanceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}

    <style>
        .bg-light {
            background-color: #f8f9fa !important;
        }

        .bg-purple {
            background-color: #6f42c1;
        }

        .text-purple {
            color: #6f42c1;
        }

        .icon-circle {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .card {
            transition: all 0.2s ease;
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
        }

        .card-header {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .chart-container {
            position: relative;
            height: 250px;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .badge {
            font-weight: 500;
        }

        .dashboard-stat .stat-icon {
            width: 50px;
            height: 50px;
        }
    </style>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Configuration des couleurs
        const colors = {
            primary: '#0275d8',
            secondary: '#6c757d',
            success: '#28a745',
            danger: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8',
            light: '#f8f9fa',
            dark: '#343a40',
            purple: '#6f42c1',
            pink: '#e83e8c',
            orange: '#fd7e14',
            teal: '#20c997'
        };

        const colorPalette = [
            colors.primary,
            colors.purple,
            colors.success,
            colors.danger,
            colors.warning,
            colors.info,
            colors.teal,
            colors.pink,
            colors.orange,
            colors.secondary
        ];

        // Options communes pour tous les graphiques
        const commonOptions = {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 1000,
                easing: 'easeOutQuart'
            },
            plugins: {
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    titleFont: {
                        size: 13,
                        weight: 'bold'
                    },
                    bodyFont: {
                        size: 12
                    },
                    padding: 10,
                    cornerRadius: 4,
                    displayColors: true
                }
            }
        };

        {% if widgets.document_stats is defined and widgets.document_stats %}
            // Graphique de répartition par type de document
            const documentTypeData = {
                labels: [{% for type, count in dashboard_data.document_stats.by_type %}'{{ type }}',{% endfor %}],
                datasets: [{
                    data: [{% for type, count in dashboard_data.document_stats.by_type %}{{ count }},{% endfor %}],
                    backgroundColor: colorPalette,
                    borderWidth: 1,
                    borderColor: '#fff'
                }]
            };

            new Chart(
                document.getElementById('documentTypeChart'),
                {
                    type: 'pie',
                    data: documentTypeData,
                    options: {
                        ...commonOptions,
                        plugins: {
                            ...commonOptions.plugins,
                            legend: {
                                position: 'right',
                                labels: {
                                    padding: 15,
                                    usePointStyle: true,
                                    pointStyle: 'circle',
                                    font: {
                                        size: 11
                                    }
                                }
                            },
                            title: {
                                display: false,
                                text: 'Répartition par type de document',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                },
                                padding: {
                                    bottom: 15
                                }
                            }
                        },
                        cutout: '0%'
                    }
                }
            );
        {% endif %}

        {% if widgets.workflow_stats is defined and widgets.workflow_stats %}
            // Graphique de répartition par état
            const stateDistributionData = {
                labels: [{% for state, count in dashboard_data.workflow_stats.state_distribution|slice(0, 10) %}'{{ state }}',{% endfor %}],
                datasets: [{
                    label: 'Nombre de documents',
                    data: [{% for state, count in dashboard_data.workflow_stats.state_distribution|slice(0, 10) %}{{ count }},{% endfor %}],
                    backgroundColor: colors.success,
                    borderRadius: 4,
                    barThickness: 12,
                    maxBarThickness: 18
                }]
            };

            new Chart(
                document.getElementById('stateDistributionChart'),
                {
                    type: 'bar',
                    data: stateDistributionData,
                    options: {
                        ...commonOptions,
                        plugins: {
                            ...commonOptions.plugins,
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)',
                                    drawBorder: false
                                },
                                ticks: {
                                    font: {
                                        size: 11
                                    }
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    font: {
                                        size: 10
                                    }
                                }
                            }
                        }
                    }
                }
            );
        {% endif %}

        {% if widgets.processing_time_trends is defined and widgets.processing_time_trends %}
            // Graphique des tendances de temps de traitement
            const processingTimeTrendsData = {
                labels: [{% for period, data in dashboard_data.processing_time_trends %}'{{ data.label }}',{% endfor %}],
                datasets: [{
                    label: 'Temps moyen (jours)',
                    data: [{% for period, data in dashboard_data.processing_time_trends %}{{ data.avg_processing_time }},{% endfor %}],
                    borderColor: colors.warning,
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    fill: true,
                    tension: 0.4,
                    borderWidth: 2,
                    pointBackgroundColor: colors.warning,
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            };

            new Chart(
                document.getElementById('processingTimeTrendsChart'),
                {
                    type: 'line',
                    data: processingTimeTrendsData,
                    options: {
                        ...commonOptions,
                        plugins: {
                            ...commonOptions.plugins,
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)',
                                    drawBorder: false
                                },
                                ticks: {
                                    font: {
                                        size: 11
                                    },
                                    callback: function(value) {
                                        return value + ' j';
                                    }
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    font: {
                                        size: 11
                                    }
                                }
                            }
                        }
                    }
                }
            );
        {% endif %}

        {% if widgets.document_distribution is defined and widgets.document_distribution %}
            // Graphique de répartition par département
            const departmentDistributionData = {
                labels: [{% for dept, count in dashboard_data.document_distribution.by_department %}'{{ dept }}',{% endfor %}],
                datasets: [{
                    label: 'Nombre de documents',
                    data: [{% for dept, count in dashboard_data.document_distribution.by_department %}{{ count }},{% endfor %}],
                    backgroundColor: colorPalette,
                    borderWidth: 1,
                    borderColor: '#fff'
                }]
            };

            new Chart(
                document.getElementById('departmentDistributionChart'),
                {
                    type: 'doughnut',
                    data: departmentDistributionData,
                    options: {
                        ...commonOptions,
                        plugins: {
                            ...commonOptions.plugins,
                            legend: {
                                position: 'right',
                                labels: {
                                    padding: 15,
                                    usePointStyle: true,
                                    pointStyle: 'circle',
                                    font: {
                                        size: 11
                                    }
                                }
                            }
                        },
                        cutout: '60%'
                    }
                }
            );

            // Données pour les différentes vues de répartition par utilisateur
            const userDistributionDatasets = {
                by_supervisor: {
                    labels: [{% for userId, userData in dashboard_data.document_distribution.by_user.by_supervisor|slice(0, 10) %}'{{ userData.name }}',{% endfor %}],
                    data: [{% for userId, userData in dashboard_data.document_distribution.by_user.by_supervisor|slice(0, 10) %}{{ userData.count }},{% endfor %}],
                    label: 'Nombre de documents',
                    unit: ''
                },
                by_recent_activity: {
                    labels: [{% for userId, userData in dashboard_data.document_distribution.by_user.by_recent_activity|slice(0, 10) %}'{{ userData.name }}',{% endfor %}],
                    data: [{% for userId, userData in dashboard_data.document_distribution.by_user.by_recent_activity|slice(0, 10) %}{{ userData.count }},{% endfor %}],
                    label: 'Nombre d\'activités (30 derniers jours)',
                    unit: ''
                },
                by_processing_time: {
                    labels: [{% for userId, userData in dashboard_data.document_distribution.by_user.by_processing_time|slice(0, 10) %}'{{ userData.name }}',{% endfor %}],
                    data: [{% for userId, userData in dashboard_data.document_distribution.by_user.by_processing_time|slice(0, 10) %}{{ userData.count }},{% endfor %}],
                    label: 'Temps moyen de traitement',
                    unit: 'h'
                }
            };

            // Configuration initiale du graphique de répartition par utilisateur
            const userDistributionData = {
                labels: userDistributionDatasets.by_supervisor.labels,
                datasets: [{
                    label: userDistributionDatasets.by_supervisor.label,
                    data: userDistributionDatasets.by_supervisor.data,
                    backgroundColor: colors.purple,
                    borderRadius: 4,
                    barThickness: 12,
                    maxBarThickness: 18
                }]
            };

            const userDistributionChart = new Chart(
                document.getElementById('userDistributionChart'),
                {
                    type: 'bar',
                    data: userDistributionData,
                    options: {
                        ...commonOptions,
                        indexAxis: 'y',
                        plugins: {
                            ...commonOptions.plugins,
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            x: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)',
                                    drawBorder: false
                                },
                                ticks: {
                                    font: {
                                        size: 11
                                    },
                                    callback: function(value) {
                                        return value + userDistributionDatasets.by_supervisor.unit;
                                    }
                                }
                            },
                            y: {
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    font: {
                                        size: 10
                                    }
                                }
                            }
                        }
                    }
                }
            );

            // Gestion du changement de vue pour la répartition par utilisateur
            document.getElementById('userDistributionType').addEventListener('change', function() {
                const viewType = this.value;
                const dataset = userDistributionDatasets[viewType];

                // Mettre à jour les données du graphique
                userDistributionChart.data.labels = dataset.labels;
                userDistributionChart.data.datasets[0].label = dataset.label;
                userDistributionChart.data.datasets[0].data = dataset.data;

                // Mettre à jour l'unité dans les ticks
                userDistributionChart.options.scales.x.ticks.callback = function(value) {
                    return value + dataset.unit;
                };

                // Si c'est le temps de traitement, inverser l'ordre (les plus rapides d'abord)
                if (viewType === 'by_processing_time') {
                    userDistributionChart.options.indexAxis = 'y';
                } else {
                    userDistributionChart.options.indexAxis = 'y';
                }

                userDistributionChart.update();
            });
        {% endif %}

        {% if widgets.department_performance is defined and widgets.department_performance %}
            // Graphique de performance par département
            const departmentPerformanceData = {
                labels: [{% for dept in dashboard_data.department_performance.departments %}'{{ dept.name }}',{% endfor %}],
                datasets: [
                    {
                        label: 'Temps de traitement moyen (jours)',
                        data: [{% for dept in dashboard_data.department_performance.departments %}{{ dept.processing_time }},{% endfor %}],
                        backgroundColor: colors.success,
                        borderRadius: 4,
                        barThickness: 16,
                        maxBarThickness: 20,
                        hidden: false
                    },
                    {
                        label: 'Nombre de documents traités',
                        data: [{% for dept in dashboard_data.department_performance.departments %}{{ dept.document_count }},{% endfor %}],
                        backgroundColor: colors.primary,
                        borderRadius: 4,
                        barThickness: 16,
                        maxBarThickness: 20,
                        hidden: true
                    },
                    {
                        label: 'Taux de rejet (%)',
                        data: [{% for dept in dashboard_data.department_performance.departments %}{{ dept.rejection_rate }},{% endfor %}],
                        backgroundColor: colors.danger,
                        borderRadius: 4,
                        barThickness: 16,
                        maxBarThickness: 20,
                        hidden: true
                    }
                ]
            };

            const departmentPerformanceChart = new Chart(
                document.getElementById('departmentPerformanceChart'),
                {
                    type: 'bar',
                    data: departmentPerformanceData,
                    options: {
                        ...commonOptions,
                        plugins: {
                            ...commonOptions.plugins,
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)',
                                    drawBorder: false
                                },
                                ticks: {
                                    font: {
                                        size: 11
                                    }
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    font: {
                                        size: 11
                                    }
                                }
                            }
                        }
                    }
                }
            );

            // Gestion des boutons de métriques pour le graphique de performance
            document.querySelectorAll('.btn-group [data-metric]').forEach(button => {
                button.addEventListener('click', function() {
                    const metric = this.getAttribute('data-metric');

                    // Mettre à jour les boutons actifs
                    document.querySelectorAll('.btn-group [data-metric]').forEach(btn => {
                        btn.classList.remove('active');
                    });
                    this.classList.add('active');

                    // Afficher le dataset correspondant
                    departmentPerformanceChart.data.datasets.forEach((dataset, index) => {
                        if (index === 0) { // Temps de traitement
                            dataset.hidden = metric !== 'processing_time';
                        } else if (index === 1) { // Nombre de documents
                            dataset.hidden = metric !== 'document_count';
                        } else if (index === 2) { // Taux de rejet
                            dataset.hidden = metric !== 'rejection_rate';
                        }
                    });

                    departmentPerformanceChart.update();
                });
            });
        {% endif %}

        // Initialisation des cercles de progression pour les tâches en attente
        document.querySelectorAll('.progress-circle').forEach(circle => {
            const value = parseInt(circle.getAttribute('data-value'));
            const size = parseInt(circle.getAttribute('data-size'));
            const thickness = parseInt(circle.getAttribute('data-thickness'));
            const color = circle.getAttribute('data-color');

            const radius = (size - thickness) / 2;
            const circumference = 2 * Math.PI * radius;
            const strokeDashoffset = circumference - (value / 100) * circumference;

            // Créer le SVG
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.setAttribute('width', size);
            svg.setAttribute('height', size);
            svg.setAttribute('viewBox', `0 0 ${size} ${size}`);

            // Créer le cercle de fond
            const bgCircle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            bgCircle.setAttribute('cx', size / 2);
            bgCircle.setAttribute('cy', size / 2);
            bgCircle.setAttribute('r', radius);
            bgCircle.setAttribute('fill', 'none');
            bgCircle.setAttribute('stroke', '#f0f0f0');
            bgCircle.setAttribute('stroke-width', thickness);

            // Créer le cercle de progression
            const progressCircle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            progressCircle.setAttribute('cx', size / 2);
            progressCircle.setAttribute('cy', size / 2);
            progressCircle.setAttribute('r', radius);
            progressCircle.setAttribute('fill', 'none');
            progressCircle.setAttribute('stroke', color);
            progressCircle.setAttribute('stroke-width', thickness);
            progressCircle.setAttribute('stroke-dasharray', circumference);
            progressCircle.setAttribute('stroke-dashoffset', strokeDashoffset);
            progressCircle.setAttribute('transform', `rotate(-90 ${size / 2} ${size / 2})`);

            // Ajouter les cercles au SVG
            svg.appendChild(bgCircle);
            svg.appendChild(progressCircle);

            // Remplacer le contenu du div par le SVG et la valeur
            const valueDiv = circle.querySelector('.progress-circle-value');
            circle.innerHTML = '';
            circle.appendChild(svg);

            // Ajouter la valeur au centre
            const textElement = document.createElement('div');
            textElement.style.position = 'absolute';
            textElement.style.top = '50%';
            textElement.style.left = '50%';
            textElement.style.transform = 'translate(-50%, -50%)';
            textElement.style.fontSize = '10px';
            textElement.style.fontWeight = 'bold';
            textElement.style.color = color;
            textElement.textContent = value;

            circle.style.position = 'relative';
            circle.appendChild(textElement);
        });
    });
</script>
{% endblock %}
