<?php

namespace App\Service;

use App\Entity\Document;
use App\Entity\User;
use App\Entity\Commentaire;
use App\Repository\DocumentRepository;
use App\Repository\CommentaireRepository;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;

class DashboardWidgetService
{
    private EntityManagerInterface $entityManager;
    private DocumentRepository $documentRepository;
    private CommentaireRepository $commentaireRepository;
    private UserRepository $userRepository;
    private Security $security;

    public function __construct(
        EntityManagerInterface $entityManager,
        DocumentRepository $documentRepository,
        CommentaireRepository $commentaireRepository,
        UserRepository $userRepository,
        Security $security
    ) {
        $this->entityManager = $entityManager;
        $this->documentRepository = $documentRepository;
        $this->commentaireRepository = $commentaireRepository;
        $this->userRepository = $userRepository;
        $this->security = $security;
    }

    /**
     * Récupère les activités récentes sur les documents
     */
    public function getRecentActivities(int $limit = 10): array
    {
        $activities = [];
        $allUpdates = [];

        // Récupérer les documents récemment modifiés
        $recentDocuments = $this->documentRepository->findBy([], ['id' => 'DESC'], $limit * 3);

        foreach ($recentDocuments as $document) {
            $updates = $document->getUpdates();

            if ($updates) {
                foreach ($updates as $update) {
                    $updateDate = new \DateTime($update['date']);
                    $updateUser = null;

                    if (isset($update['user_id'])) {
                        $updateUser = $this->userRepository->find($update['user_id']);
                    }

                    $updateType = $update['type'];
                    $updateDetails = $update['details'] ?? '';

                    // Déterminer l'icône et la couleur en fonction du type de mise à jour
                    $icon = 'fa-edit';
                    $color = 'primary';
                    $message = 'Document mis à jour';

                    switch ($updateType) {
                        case 'create':
                            $icon = 'fa-plus-circle';
                            $color = 'success';
                            $message = 'Document créé';
                            break;
                        case 'visa':
                            $icon = 'fa-check-circle';
                            $color = 'success';
                            $message = 'Visa ajouté';
                            break;
                        case 'state_change':
                            $icon = 'fa-exchange-alt';
                            $color = 'warning';
                            $message = 'État changé';
                            break;
                        case 'edit':
                            $icon = 'fa-edit';
                            $color = 'primary';
                            $message = 'Document modifié';
                            break;
                        case 'comment':
                            $icon = 'fa-comment';
                            $color = 'info';
                            $message = 'Commentaire ajouté';
                            break;
                    }

                    if ($updateDetails) {
                        $message .= ' - ' . $updateDetails;
                    }

                    $allUpdates[] = [
                        'type' => $updateType,
                        'document' => $document,
                        'date' => $updateDate,
                        'user' => $updateUser ?: $document->getSuperviseur(),
                        'message' => $message,
                        'icon' => $icon,
                        'color' => $color,
                        'reference' => $document->getReference(),
                        'title' => $document->getRefTitleFra()
                    ];
                }
            }
        }

        // Récupérer les commentaires récents (pour les documents qui n'ont pas encore le champ updates)
        $recentComments = $this->commentaireRepository->findBy([], ['created_at' => 'DESC'], $limit);

        foreach ($recentComments as $comment) {
            // Vérifier si ce commentaire n'est pas déjà inclus dans les mises à jour
            $document = $comment->getDocuments();
            $commentDate = $comment->getCreatedAt();

            // Vérifier si ce commentaire est déjà inclus dans les mises à jour
            $alreadyIncluded = false;
            foreach ($allUpdates as $update) {
                if ($update['type'] === 'comment' &&
                    $update['document']->getId() === $document->getId() &&
                    abs($update['date']->getTimestamp() - $commentDate->getTimestamp()) < 60) {
                    $alreadyIncluded = true;
                    break;
                }
            }

            if (!$alreadyIncluded) {
                $allUpdates[] = [
                    'type' => 'comment',
                    'document' => $document,
                    'date' => $commentDate,
                    'user' => $comment->getUser(),
                    'message' => 'Commentaire ajouté',
                    'content' => $comment->getCommentaire(),
                    'icon' => 'fa-comment',
                    'color' => 'info',
                    'reference' => $document->getReference(),
                    'title' => $document->getRefTitleFra()
                ];
            }
        }

        // Trier toutes les mises à jour par date (la plus récente en premier)
        usort($allUpdates, function($a, $b) {
            return $b['date'] <=> $a['date'];
        });

        // Limiter le nombre de mises à jour et les ajouter aux activités
        $activities = array_slice($allUpdates, 0, $limit);

        // Ajouter des informations supplémentaires pour l'affichage
        foreach ($activities as &$activity) {
            // Formater la date pour l'affichage
            $now = new \DateTime();
            $interval = $now->diff($activity['date']);

            if ($interval->days == 0) {
                if ($interval->h == 0) {
                    if ($interval->i == 0) {
                        $activity['time_ago'] = 'à l\'instant';
                    } else {
                        $activity['time_ago'] = 'il y a ' . $interval->i . ' minute' . ($interval->i > 1 ? 's' : '');
                    }
                } else {
                    $activity['time_ago'] = 'il y a ' . $interval->h . ' heure' . ($interval->h > 1 ? 's' : '');
                }
            } elseif ($interval->days == 1) {
                $activity['time_ago'] = 'hier';
            } elseif ($interval->days < 7) {
                $activity['time_ago'] = 'il y a ' . $interval->days . ' jour' . ($interval->days > 1 ? 's' : '');
            } else {
                $activity['time_ago'] = $activity['date']->format('d/m/Y');
            }

            // Ajouter l'heure exacte
            $activity['exact_time'] = $activity['date']->format('H:i');
        }

        return $activities;
    }

    /**
     * Récupère les changements d'état récents
     */
    private function getRecentStateChanges(int $limit = 10): array
    {
        $stateChanges = [];
        $documents = $this->documentRepository->findBy([], ['id' => 'DESC'], $limit * 2);

        foreach ($documents as $document) {
            $timestamps = $document->getRawStateTimestamps();
            if (!$timestamps) {
                continue;
            }

            foreach ($timestamps as $state => $entries) {
                if (is_array($entries)) {
                    foreach ($entries as $entry) {
                        if (isset($entry['enter']) && isset($entry['from_state'])) {
                            $stateChanges[] = [
                                'document' => $document,
                                'date' => new \DateTime($entry['enter']),
                                'from_state' => $entry['from_state'],
                                'to_state' => $state,
                                'user' => $entry['user'] ?? null
                            ];
                        }
                    }
                }
            }
        }

        // Trier par date (la plus récente en premier)
        usort($stateChanges, function($a, $b) {
            return $b['date'] <=> $a['date'];
        });

        return array_slice($stateChanges, 0, $limit);
    }

    /**
     * Récupère les validations récentes
     */
    private function getRecentValidations(int $limit = 10): array
    {
        $validations = [];
        $documents = $this->documentRepository->findBy([], ['id' => 'DESC'], $limit * 2);

        foreach ($documents as $document) {
            $visas = $document->getVisas();
            if (!$visas) {
                continue;
            }

            foreach ($visas as $visa) {
                if ($visa->getDateVisa()) {
                    $validations[] = [
                        'document' => $document,
                        'date' => $visa->getDateVisa(),
                        'user' => $visa->getValidator(),
                        'state' => $visa->getName()
                    ];
                }
            }
        }

        // Trier par date (la plus récente en premier)
        usort($validations, function($a, $b) {
            return $b['date'] <=> $a['date'];
        });

        return array_slice($validations, 0, $limit);
    }

    /**
     * Récupère les tâches en attente pour l'utilisateur connecté
     */
    public function getPendingTasks(): array
    {
        $user = $this->security->getUser();
        if (!$user) {
            return [];
        }

        // Récupérer les activités récentes (filtrer pour l'utilisateur courant)
        $allActivities = $this->getRecentActivities(20); // Récupérer plus d'activités pour pouvoir filtrer
        $userActivities = [];

        foreach ($allActivities as $activity) {
            if (isset($activity['user']) && $activity['user'] && $activity['user']->getId() === $user->getId()) {
                $userActivities[] = $activity;
            }
        }

        // Limiter à 10 activités
        $userActivities = array_slice($userActivities, 0, 10);

        $pendingTasks = [
            'to_validate' => [],
            'to_review' => [],
            'managed_places' => [],
            'recent_activities' => $userActivities,
            'total' => 0
        ];

        // Documents à valider (où l'utilisateur a un visa non signé)
        $documentsToValidate = $this->documentRepository->findDocumentsToValidateByUser($user);

        foreach ($documentsToValidate as $document) {
            $urgency = $this->calculateUrgency($document);

            $pendingTasks['to_validate'][] = [
                'document' => $document,
                'urgency' => $urgency,
                'task_type' => 'validation',
                'state' => $document->getCurrentSteps() ? array_keys($document->getCurrentSteps())[0] : 'unknown'
            ];
        }

        // Documents à réviser (documents retournés à l'utilisateur)
        $documentsToReview = $this->documentRepository->findDocumentsToReviewByUser($user);

        foreach ($documentsToReview as $document) {
            $urgency = $this->calculateUrgency($document);

            $pendingTasks['to_review'][] = [
                'document' => $document,
                'urgency' => $urgency,
                'task_type' => 'review',
                'state' => $document->getCurrentSteps() ? array_keys($document->getCurrentSteps())[0] : 'unknown'
            ];
        }

        // Documents dans les places gérées par l'utilisateur
        $managedPlaces = $user->getManagedPlaces() ?? [];

        foreach ($managedPlaces as $place) {
            // Utiliser la méthode optimisée pour récupérer directement les documents actifs
            if ($place === 'Qual_Logistique' || $place === 'Logistique') {
                $documentsToProcess = $this->documentRepository->findActiveDocumentsInLogisticsSteps();
            } else {
                $documentsToProcess = $this->documentRepository->findActiveDocumentsInStep($place);
            }

            if (!empty($documentsToProcess)) {
                $pendingTasks['managed_places'][$place] = [
                    'documents' => [],
                    'count' => count($documentsToProcess)
                ];

                foreach ($documentsToProcess as $document) {
                    $urgency = $this->calculateUrgency($document);

                    $pendingTasks['managed_places'][$place]['documents'][] = [
                        'document' => $document,
                        'urgency' => $urgency,
                        'task_type' => 'managed_place',
                        'state' => $place
                    ];
                }

                // Trier les documents par urgence
                usort($pendingTasks['managed_places'][$place]['documents'], function($a, $b) {
                    return $b['urgency'] <=> $a['urgency'];
                });
            }
        }

        // Trier les tâches par urgence
        usort($pendingTasks['to_validate'], function($a, $b) {
            return $b['urgency'] <=> $a['urgency'];
        });

        usort($pendingTasks['to_review'], function($a, $b) {
            return $b['urgency'] <=> $a['urgency'];
        });

        // Calculer le total des tâches en attente (uniquement les places supervisées)
        $managedPlacesCount = 0;
        foreach ($pendingTasks['managed_places'] as $place => $data) {
            $managedPlacesCount += $data['count'];
        }

        $pendingTasks['total'] = $managedPlacesCount;

        return $pendingTasks;
    }



    /**
     * Calcule l'urgence d'un document (0-100)
     */
    private function calculateUrgency(Document $document): int
    {
        $urgency = 0;
        $now = new \DateTime();

        // Facteur 1: Temps passé dans l'état actuel
        $currentSteps = $document->getCurrentSteps();
        if ($currentSteps) {
            $state = array_keys($currentSteps)[0];
            $timestamps = $document->getRawStateTimestamps();

            if (isset($timestamps[$state])) {
                $entries = $timestamps[$state];
                $lastEntry = is_array($entries) ? end($entries) : null;

                if ($lastEntry && isset($lastEntry['enter'])) {
                    $enterDate = new \DateTime($lastEntry['enter']);
                    $daysSinceEnter = $now->diff($enterDate)->days;

                    // Plus le document est dans cet état depuis longtemps, plus c'est urgent
                    $urgency += min(50, $daysSinceEnter * 5); // Max 50 points pour ce facteur
                }
            }
        }

        // Facteur 2: Type de document (certains types sont plus urgents)
        $docType = $document->getDocType();
        if ($docType) {
            // Exemple: les documents de type "URGENT" sont plus prioritaires
            if (stripos($docType, 'urgent') !== false) {
                $urgency += 30;
            }
            // Autres types prioritaires
            elseif (in_array($docType, ['SPEC', 'PLAN'])) {
                $urgency += 20;
            }
            else {
                $urgency += 10;
            }
        }

        // Facteur 3: Nombre de retours/rejets (plus un document a été rejeté, plus il est urgent)
        // Cette logique dépend de votre modèle de données

        // Limiter l'urgence à 100
        return min(100, $urgency);
    }
}
