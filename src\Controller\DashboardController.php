<?php

namespace App\Controller;

use App\Entity\UserPreference;
use App\Repository\DocumentRepository;
use App\Repository\UserPreferenceRepository;
use App\Repository\UserRepository;
use App\Service\DataAnalysisService;
use App\Service\DashboardWidgetService;
use App\Service\DepartmentPerformanceService;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/dashboard')]
class DashboardController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private UserPreferenceRepository $userPreferenceRepository;
    private DocumentRepository $documentRepository;
    private UserRepository $userRepository;
    private DataAnalysisService $dataAnalysisService;

    private DashboardWidgetService $dashboardWidgetService;
    private DepartmentPerformanceService $departmentPerformanceService;

    public function __construct(
        EntityManagerInterface $entityManager,
        UserPreferenceRepository $userPreferenceRepository,
        DocumentRepository $documentRepository,
        UserRepository $userRepository,
        DataAnalysisService $dataAnalysisService,

        DashboardWidgetService $dashboardWidgetService,
        DepartmentPerformanceService $departmentPerformanceService
    ) {
        $this->entityManager = $entityManager;
        $this->userPreferenceRepository = $userPreferenceRepository;
        $this->documentRepository = $documentRepository;
        $this->userRepository = $userRepository;
        $this->dataAnalysisService = $dataAnalysisService;

        $this->dashboardWidgetService = $dashboardWidgetService;
        $this->departmentPerformanceService = $departmentPerformanceService;
    }

    #[Route('/', name: 'app_dashboard', methods: ['GET'])]
    public function index(): Response
    {
        $user = $this->getUser();

        // Récupérer les préférences de l'utilisateur
        $dashboardPreference = $this->userPreferenceRepository->findOneByKey($user, 'dashboard_widgets');

        // Widgets par défaut si aucune préférence n'est définie
        $defaultWidgets = [
            'recent_documents' => true,
            'document_stats' => true,
            'workflow_stats' => true,
            'risky_documents' => true,
            'processing_time_trends' => true,
            'document_distribution' => true,
        ];

        $widgets = $dashboardPreference ? $dashboardPreference->getPreferenceValue() : $defaultWidgets;

        // Récupérer les données pour les widgets activés
        $dashboardData = [];

        if ($widgets['recent_documents'] ?? false) {
            $dashboardData['recent_documents'] = $this->documentRepository->findBy([], ['id' => 'DESC'], 10);
        }

        if ($widgets['document_stats'] ?? false) {
            $documents = $this->documentRepository->findAll();
            $dashboardData['document_stats'] = [
                'total' => count($documents),
                'by_type' => $this->getDocumentCountByType($documents),
            ];
        }

        if ($widgets['workflow_stats'] ?? false) {
            $documents = $this->documentRepository->findAll();
            $dashboardData['workflow_stats'] = [
                'state_distribution' => $this->getStateDistribution($documents),
                'average_time_by_state' => $this->getAverageTimeByState($documents),
            ];
        }

        if ($widgets['risky_documents'] ?? false) {
            $dashboardData['risky_documents'] = $this->dataAnalysisService->identifyRiskyDocuments();
        }

        if ($widgets['processing_time_trends'] ?? false) {
            $dashboardData['processing_time_trends'] = $this->dataAnalysisService->analyzeProcessingTimeTrends();
        }

        if ($widgets['document_distribution'] ?? false) {
            $documents = $this->documentRepository->findAll();
            $dashboardData['document_distribution'] = [
                'by_department' => $this->getDocumentDistributionByDepartment($documents),
                'by_user' => $this->getDocumentDistributionByUser($documents),
            ];
        }

        // Nouveaux widgets
        if ($widgets['recent_activities'] ?? false) {
            $dashboardData['recent_activities'] = $this->dashboardWidgetService->getRecentActivities();
        }

        if ($widgets['pending_tasks'] ?? false) {
            $dashboardData['pending_tasks'] = $this->dashboardWidgetService->getPendingTasks();
        }

        if ($widgets['department_performance'] ?? false) {
            $dashboardData['department_performance'] = $this->departmentPerformanceService->getDepartmentPerformance();
        }



        return $this->render('dashboard/index.html.twig', [
            'widgets' => $widgets,
            'dashboard_data' => $dashboardData,
        ]);
    }

    #[Route('/customize', name: 'app_dashboard_customize', methods: ['GET'])]
    public function customize(): Response
    {
        $user = $this->getUser();

        // Récupérer les préférences de l'utilisateur
        $dashboardPreference = $this->userPreferenceRepository->findOneByKey($user, 'dashboard_widgets');

        // Widgets par défaut si aucune préférence n'est définie
        $defaultWidgets = [
            'recent_documents' => true,
            'document_stats' => true,
            'workflow_stats' => true,
            'risky_documents' => true,
            'processing_time_trends' => true,
            'document_distribution' => true,
        ];

        $widgets = $dashboardPreference ? $dashboardPreference->getPreferenceValue() : $defaultWidgets;

        // Liste de tous les widgets disponibles avec leurs descriptions
        $availableWidgets = [
            'recent_documents' => [
                'title' => 'Documents récents',
                'description' => 'Affiche les documents les plus récents',
                'icon' => 'fa-file-alt',
            ],
            'document_stats' => [
                'title' => 'Statistiques des documents',
                'description' => 'Affiche des statistiques générales sur les documents',
                'icon' => 'fa-chart-pie',
            ],
            'workflow_stats' => [
                'title' => 'Statistiques du workflow',
                'description' => 'Affiche des statistiques sur le workflow',
                'icon' => 'fa-project-diagram',
            ],
            'risky_documents' => [
                'title' => 'Documents à risque',
                'description' => 'Affiche les documents qui stagnent dans un état',
                'icon' => 'fa-exclamation-triangle',
            ],
            'processing_time_trends' => [
                'title' => 'Tendances des temps de traitement',
                'description' => 'Affiche l\'évolution des temps de traitement',
                'icon' => 'fa-chart-line',
            ],
            'document_distribution' => [
                'title' => 'Répartition des documents',
                'description' => 'Affiche la répartition des documents par département et utilisateur',
                'icon' => 'fa-users',
            ],
            'recent_activities' => [
                'title' => 'Activité récente',
                'description' => 'Affiche les dernières actions effectuées sur les documents',
                'icon' => 'fa-history',
            ],
            'pending_tasks' => [
                'title' => 'Mes tâches en attente',
                'description' => 'Affiche les documents qui attendent une action de votre part',
                'icon' => 'fa-tasks',
            ],
            'department_performance' => [
                'title' => 'Performance par département',
                'description' => 'Compare les performances des différents départements',
                'icon' => 'fa-building',
            ],
        ];

        return $this->render('dashboard/customize.html.twig', [
            'widgets' => $widgets,
            'available_widgets' => $availableWidgets,
        ]);
    }

    #[Route('/save-preferences', name: 'app_dashboard_save_preferences', methods: ['POST'])]
    public function savePreferences(Request $request): JsonResponse
    {
        $user = $this->getUser();
        $data = json_decode($request->getContent(), true);

        if (!isset($data['widgets']) || !is_array($data['widgets'])) {
            return new JsonResponse(['success' => false, 'message' => 'Données invalides'], 400);
        }

        // Récupérer ou créer la préférence
        $preference = $this->userPreferenceRepository->findOneByKey($user, 'dashboard_widgets');

        if (!$preference) {
            $preference = new UserPreference();
            $preference->setUser($user);
            $preference->setPreferenceKey('dashboard_widgets');
        }

        $preference->setPreferenceValue($data['widgets']);
        $preference->updateTimestamp();

        $this->entityManager->persist($preference);
        $this->entityManager->flush();

        return new JsonResponse(['success' => true]);
    }

    /**
     * Compte les documents par type
     */
    private function getDocumentCountByType(array $documents): array
    {
        $countByType = [];

        foreach ($documents as $document) {
            $docType = $document->getDocType();

            if (!$docType) {
                continue;
            }

            if (!isset($countByType[$docType])) {
                $countByType[$docType] = 0;
            }

            $countByType[$docType]++;
        }

        return $countByType;
    }

    /**
     * Calcule la répartition des documents par état
     */
    private function getStateDistribution(array $documents): array
    {
        $distribution = [];

        foreach ($documents as $document) {
            $currentSteps = $document->getCurrentSteps();
            if (!$currentSteps) {
                continue;
            }

            foreach (array_keys($currentSteps) as $state) {
                if (!isset($distribution[$state])) {
                    $distribution[$state] = 0;
                }
                $distribution[$state]++;
            }
        }

        // Trier par nombre de documents décroissant
        arsort($distribution);

        return $distribution;
    }

    /**
     * Calcule le temps moyen passé dans chaque état
     */
    private function getAverageTimeByState(array $documents): array
    {
        $totalTimeByState = [];
        $countByState = [];

        foreach ($documents as $document) {
            $rawTimestamps = $document->getRawStateTimestamps();
            if (!$rawTimestamps) {
                continue;
            }

            foreach ($rawTimestamps as $state => $entries) {
                $totalDays = $document->getTotalDaysInState($state);

                if ($totalDays === null) {
                    continue;
                }

                if (!isset($totalTimeByState[$state])) {
                    $totalTimeByState[$state] = 0;
                    $countByState[$state] = 0;
                }

                $totalTimeByState[$state] += $totalDays;
                $countByState[$state]++;
            }
        }

        // Calculer les moyennes
        $averageTimeByState = [];
        foreach ($totalTimeByState as $state => $totalTime) {
            if ($countByState[$state] > 0) {
                $averageTimeByState[$state] = round($totalTime / $countByState[$state], 1);
            } else {
                $averageTimeByState[$state] = 0;
            }
        }

        // Trier par temps moyen décroissant
        arsort($averageTimeByState);

        return $averageTimeByState;
    }

    /**
     * Calcule la répartition des documents par état actuel
     */
    private function getDocumentDistributionByDepartment(array $documents): array
    {
        $distribution = [];

        foreach ($documents as $document) {
            $currentSteps = $document->getCurrentSteps();

            if (!$currentSteps || empty($currentSteps)) {
                continue;
            }

            // Prendre le premier état actuel du document
            $currentState = array_keys($currentSteps)[0];

            // Rendre le nom de l'état plus lisible
            $stateName = $this->formatStateName($currentState);

            if (!isset($distribution[$stateName])) {
                $distribution[$stateName] = 0;
            }

            $distribution[$stateName]++;
        }

        // Trier par nombre de documents décroissant
        arsort($distribution);

        return $distribution;
    }

    /**
     * Formate le nom d'un état pour l'affichage
     */
    private function formatStateName(string $state): string
    {
        // Remplacer les underscores par des espaces
        $formatted = str_replace('_', ' ', $state);

        // Mettre en majuscule la première lettre de chaque mot
        $formatted = ucwords($formatted);

        // Cas spéciaux
        $replacements = [
            'Be 0' => 'Bureau d\'Études (Création)',
            'Be 1' => 'Bureau d\'Études (Vérification)',
            'Be' => 'Bureau d\'Études (Validation)',
            'Qual Logistique' => 'Qualité & Logistique'
        ];

        return $replacements[$formatted] ?? $formatted;
    }

    /**
     * Calcule la répartition des documents par utilisateur
     */
    private function getDocumentDistributionByUser(array $documents): array
    {
        $distribution = [
            'by_supervisor' => $this->getDistributionBySupervisor($documents),
            'by_recent_activity' => $this->getDistributionByRecentActivity(),
            'by_processing_time' => $this->getDistributionByProcessingTime()
        ];

        return $distribution;
    }

    /**
     * Calcule la répartition des documents par superviseur
     */
    private function getDistributionBySupervisor(array $documents): array
    {
        $distribution = [];

        foreach ($documents as $document) {
            $superviseur = $document->getSuperviseur();

            if (!$superviseur) {
                continue;
            }

            $userId = $superviseur->getId();
            $userName = $superviseur->getPrenom() . ' ' . $superviseur->getNom();

            if (!isset($distribution[$userId])) {
                $distribution[$userId] = [
                    'name' => $userName,
                    'count' => 0,
                ];
            }

            $distribution[$userId]['count']++;
        }

        // Trier par nombre de documents décroissant
        uasort($distribution, function($a, $b) {
            return $b['count'] - $a['count'];
        });

        // Limiter à 10 utilisateurs
        return array_slice($distribution, 0, 10, true);
    }

    /**
     * Calcule la répartition par activité récente des utilisateurs
     */
    private function getDistributionByRecentActivity(): array
    {
        // Récupérer tous les utilisateurs
        $users = $this->userRepository->findAll();
        $userActivity = [];

        // Récupérer les mises à jour récentes (30 derniers jours)
        $startDate = new \DateTime('-30 days');
        $now = new \DateTime();

        foreach ($users as $user) {
            $userId = $user->getId();
            $userName = $user->getPrenom() . ' ' . $user->getNom();

            // Compter les activités pour cet utilisateur
            $activityCount = 0;

            // 1. Compter les visas créés récemment
            $visas = $this->entityManager->getRepository(\App\Entity\Visa::class)
                ->createQueryBuilder('v')
                ->where('v.validator = :user')
                ->andWhere('v.dateVisa >= :startDate')
                ->setParameter('user', $user)
                ->setParameter('startDate', $startDate)
                ->getQuery()
                ->getResult();

            $activityCount += count($visas);

            // 2. Compter les commentaires créés récemment
            $comments = $this->entityManager->getRepository(\App\Entity\Commentaire::class)
                ->createQueryBuilder('c')
                ->where('c.user = :user')
                ->andWhere('c.created_at >= :startDate')
                ->setParameter('user', $user)
                ->setParameter('startDate', $startDate)
                ->getQuery()
                ->getResult();

            $activityCount += count($comments);

            // 3. Compter les mises à jour de documents
            $documents = $this->documentRepository->findAll();
            foreach ($documents as $document) {
                $updates = $document->getUpdates() ?? [];
                foreach ($updates as $update) {
                    if (isset($update['user_id']) && $update['user_id'] == $userId) {
                        $updateDate = new \DateTime($update['date']);
                        if ($updateDate >= $startDate && $updateDate <= $now) {
                            $activityCount++;
                        }
                    }
                }
            }

            if ($activityCount > 0) {
                $userActivity[$userId] = [
                    'name' => $userName,
                    'count' => $activityCount,
                ];
            }
        }

        // Trier par nombre d'activités décroissant
        uasort($userActivity, function($a, $b) {
            return $b['count'] - $a['count'];
        });

        // Limiter à 10 utilisateurs
        return array_slice($userActivity, 0, 10, true);
    }

    /**
     * Calcule la répartition par temps de traitement moyen des utilisateurs
     */
    private function getDistributionByProcessingTime(): array
    {
        // Récupérer tous les utilisateurs
        $users = $this->userRepository->findAll();
        $userProcessingTime = [];

        foreach ($users as $user) {
            $userId = $user->getId();
            $userName = $user->getPrenom() . ' ' . $user->getNom();

            // Récupérer les visas créés par cet utilisateur
            $visas = $this->entityManager->getRepository(\App\Entity\Visa::class)
                ->createQueryBuilder('v')
                ->where('v.validator = :user')
                ->setParameter('user', $user)
                ->getQuery()
                ->getResult();

            if (empty($visas)) {
                continue;
            }

            // Calculer le temps de traitement moyen
            $totalProcessingTime = 0;
            $validVisaCount = 0;

            foreach ($visas as $visa) {
                $document = $visa->getReleasedDrawing();
                if (!$document) {
                    continue;
                }

                $visaName = $visa->getName();
                $state = str_replace('visa_', '', $visaName);

                // Calculer le temps passé dans cet état
                $stateTimestamps = $document->getRawStateTimestamps();
                if (!$stateTimestamps || !isset($stateTimestamps[$state])) {
                    continue;
                }

                $entries = $stateTimestamps[$state];
                foreach ($entries as $entry) {
                    if (isset($entry['enter']) && isset($entry['exit'])) {
                        $enterDate = new \DateTime($entry['enter']);
                        $exitDate = new \DateTime($entry['exit']);
                        $interval = $enterDate->diff($exitDate);
                        $days = $interval->days;
                        $hours = $interval->h;
                        $totalHours = $days * 24 + $hours;

                        if ($totalHours > 0) {
                            $totalProcessingTime += $totalHours;
                            $validVisaCount++;
                        }
                    }
                }
            }

            if ($validVisaCount > 0) {
                $averageProcessingTime = round($totalProcessingTime / $validVisaCount, 1);
                $userProcessingTime[$userId] = [
                    'name' => $userName,
                    'count' => $averageProcessingTime,
                    'unit' => 'heures'
                ];
            }
        }

        // Trier par temps de traitement croissant (les plus rapides d'abord)
        uasort($userProcessingTime, function($a, $b) {
            return $a['count'] - $b['count'];
        });

        // Limiter à 10 utilisateurs
        return array_slice($userProcessingTime, 0, 10, true);
    }
}
